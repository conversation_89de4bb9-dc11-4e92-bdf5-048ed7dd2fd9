<?php
/**
 * Analytics Dashboard Page
 * Phase 3: Data Intelligence & Analytics
 */

if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('edit_posts')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

// Enqueue required scripts and styles for drag and drop
wp_enqueue_script('jquery-ui-draggable');
wp_enqueue_script('jquery-ui-droppable');
wp_enqueue_script('jquery-ui-resizable');
wp_enqueue_style('dab-analytics-dashboard', plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/analytics-dashboard.css', array(), DAB_VERSION);
wp_enqueue_script('dab-analytics-dashboard', plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/analytics-dashboard.js', array('jquery', 'jquery-ui-draggable', 'jquery-ui-droppable', 'jquery-ui-resizable'), DAB_VERSION, true);

// Localize script with admin variables
wp_localize_script('dab-analytics-dashboard', 'dab_admin_vars', array(
    'ajaxurl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('dab_admin_nonce'),
    'plugin_url' => plugin_dir_url(dirname(dirname(__FILE__)))
));

// Add AJAX handler for getting data sources
add_action('wp_ajax_dab_get_analytics_data_sources', 'dab_get_analytics_data_sources_handler');

function dab_get_analytics_data_sources_handler() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    // Check user permissions
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    global $wpdb;
    $data_sources = array();

    try {
        // Get custom tables from dab_tables
        $tables_table = $wpdb->prefix . 'dab_tables';
        $custom_tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label");

        foreach ($custom_tables as $table) {
            $data_sources[] = array(
                'id' => 'custom_' . $table->id,
                'name' => $table->table_label,
                'table_slug' => $table->table_slug,
                'type' => 'custom',
                'description' => $table->description ?: 'Custom database table'
            );
        }

        // Add WordPress core tables
        $wp_tables = array(
            array(
                'id' => 'wp_users',
                'name' => 'WordPress Users',
                'table_slug' => 'users',
                'type' => 'wordpress',
                'description' => 'WordPress user accounts'
            ),
            array(
                'id' => 'wp_posts',
                'name' => 'WordPress Posts',
                'table_slug' => 'posts',
                'type' => 'wordpress',
                'description' => 'WordPress posts and pages'
            ),
            array(
                'id' => 'wp_comments',
                'name' => 'WordPress Comments',
                'table_slug' => 'comments',
                'type' => 'wordpress',
                'description' => 'WordPress comments'
            )
        );

        $data_sources = array_merge($data_sources, $wp_tables);

        wp_send_json_success($data_sources);

    } catch (Exception $e) {
        wp_send_json_error('Failed to load data sources: ' . $e->getMessage());
    }
}

$dashboard_id = isset($_GET['dashboard_id']) ? intval($_GET['dashboard_id']) : 0;
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';

?>
<div class="wrap dab-analytics-dashboard-page">
    <h1 class="wp-heading-inline">
        <?php _e('Analytics Dashboard', 'db-app-builder'); ?>
        <span class="dab-phase-badge">Phase 3</span>
    </h1>

    <?php if ($action === 'list'): ?>
        <a href="<?php echo admin_url('admin.php?page=dab_analytics_dashboard&action=create'); ?>" class="page-title-action">
            <?php _e('Create Dashboard', 'db-app-builder'); ?>
        </a>

        <div class="dab-dashboard-overview">
            <div class="dab-overview-cards">
                <div class="dab-overview-card">
                    <div class="dab-card-icon">
                        <span class="dashicons dashicons-dashboard"></span>
                    </div>
                    <div class="dab-card-content">
                        <div class="dab-card-number" id="total-dashboards">-</div>
                        <div class="dab-card-label"><?php _e('Total Dashboards', 'db-app-builder'); ?></div>
                    </div>
                </div>

                <div class="dab-overview-card">
                    <div class="dab-card-icon">
                        <span class="dashicons dashicons-chart-line"></span>
                    </div>
                    <div class="dab-card-content">
                        <div class="dab-card-number" id="active-widgets">-</div>
                        <div class="dab-card-label"><?php _e('Active Widgets', 'db-app-builder'); ?></div>
                    </div>
                </div>

                <div class="dab-overview-card">
                    <div class="dab-card-icon">
                        <span class="dashicons dashicons-visibility"></span>
                    </div>
                    <div class="dab-card-content">
                        <div class="dab-card-number" id="total-views">-</div>
                        <div class="dab-card-label"><?php _e('Total Views', 'db-app-builder'); ?></div>
                    </div>
                </div>

                <div class="dab-overview-card">
                    <div class="dab-card-icon">
                        <span class="dashicons dashicons-update"></span>
                    </div>
                    <div class="dab-card-content">
                        <div class="dab-card-number" id="realtime-updates">-</div>
                        <div class="dab-card-label"><?php _e('Real-time Updates', 'db-app-builder'); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="dab-dashboards-toolbar">
            <div class="dab-toolbar-filters">
                <select id="dashboard-filter">
                    <option value=""><?php _e('All Dashboards', 'db-app-builder'); ?></option>
                    <option value="public"><?php _e('Public Dashboards', 'db-app-builder'); ?></option>
                    <option value="private"><?php _e('Private Dashboards', 'db-app-builder'); ?></option>
                    <option value="realtime"><?php _e('Real-time Dashboards', 'db-app-builder'); ?></option>
                </select>

                <input type="text" id="search-dashboards" placeholder="<?php _e('Search dashboards...', 'db-app-builder'); ?>">
            </div>

            <div class="dab-toolbar-actions">
                <button class="button" id="refresh-dashboards">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Refresh', 'db-app-builder'); ?>
                </button>

                <button class="button" id="import-dashboard">
                    <span class="dashicons dashicons-upload"></span>
                    <?php _e('Import', 'db-app-builder'); ?>
                </button>
            </div>
        </div>

        <div class="dab-dashboards-grid" id="dashboards-grid">
            <div class="dab-loading">
                <div class="spinner is-active"></div>
                <p><?php _e('Loading dashboards...', 'db-app-builder'); ?></p>
            </div>
        </div>

    <?php elseif ($action === 'create' || $action === 'edit'): ?>
        <div class="dab-dashboard-builder">
            <div class="dab-builder-sidebar">
                <div class="dab-sidebar-section">
                    <h3><?php _e('Dashboard Settings', 'db-app-builder'); ?></h3>
                    <div class="dab-form-group">
                        <label for="dashboard-name"><?php _e('Dashboard Name', 'db-app-builder'); ?></label>
                        <input type="text" id="dashboard-name" class="dab-input" required>
                    </div>

                    <div class="dab-form-group">
                        <label for="dashboard-description"><?php _e('Description', 'db-app-builder'); ?></label>
                        <textarea id="dashboard-description" class="dab-textarea" rows="3"></textarea>
                    </div>

                    <div class="dab-form-group">
                        <label for="refresh-interval"><?php _e('Refresh Interval (seconds)', 'db-app-builder'); ?></label>
                        <select id="refresh-interval" class="dab-select">
                            <option value="10">10 seconds</option>
                            <option value="30" selected>30 seconds</option>
                            <option value="60">1 minute</option>
                            <option value="300">5 minutes</option>
                            <option value="0"><?php _e('Manual only', 'db-app-builder'); ?></option>
                        </select>
                    </div>

                    <div class="dab-form-group">
                        <label>
                            <input type="checkbox" id="auto-refresh">
                            <?php _e('Enable auto-refresh', 'db-app-builder'); ?>
                        </label>
                    </div>

                    <div class="dab-form-group">
                        <label>
                            <input type="checkbox" id="is-public">
                            <?php _e('Make dashboard public', 'db-app-builder'); ?>
                        </label>
                    </div>
                </div>

                <div class="dab-sidebar-section">
                    <h3><?php _e('Available Widgets', 'db-app-builder'); ?></h3>
                    <div class="dab-widget-library">
                        <div class="dab-widget-item" data-widget-type="metric" draggable="true">
                            <span class="dashicons dashicons-chart-area"></span>
                            <span><?php _e('Metric Card', 'db-app-builder'); ?></span>
                        </div>

                        <div class="dab-widget-item" data-widget-type="chart" draggable="true">
                            <span class="dashicons dashicons-chart-bar"></span>
                            <span><?php _e('Chart Widget', 'db-app-builder'); ?></span>
                        </div>

                        <div class="dab-widget-item" data-widget-type="table" draggable="true">
                            <span class="dashicons dashicons-list-view"></span>
                            <span><?php _e('Data Table', 'db-app-builder'); ?></span>
                        </div>

                        <div class="dab-widget-item" data-widget-type="gauge" draggable="true">
                            <span class="dashicons dashicons-performance"></span>
                            <span><?php _e('Gauge Chart', 'db-app-builder'); ?></span>
                        </div>

                        <div class="dab-widget-item" data-widget-type="map" draggable="true">
                            <span class="dashicons dashicons-location"></span>
                            <span><?php _e('Map Widget', 'db-app-builder'); ?></span>
                        </div>

                        <div class="dab-widget-item" data-widget-type="text" draggable="true">
                            <span class="dashicons dashicons-text"></span>
                            <span><?php _e('Text Widget', 'db-app-builder'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="dab-sidebar-section">
                    <h3><?php _e('Actions', 'db-app-builder'); ?></h3>
                    <div class="dab-action-buttons">
                        <button class="button button-primary" id="save-dashboard">
                            <span class="dashicons dashicons-saved"></span>
                            <?php _e('Save Dashboard', 'db-app-builder'); ?>
                        </button>

                        <button class="button" id="preview-dashboard">
                            <span class="dashicons dashicons-visibility"></span>
                            <?php _e('Preview', 'db-app-builder'); ?>
                        </button>

                        <button class="button" id="export-dashboard">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Export', 'db-app-builder'); ?>
                        </button>
                    </div>
                </div>
            </div>

            <div class="dab-dashboard-canvas">
                <div class="dab-canvas-header">
                    <div class="dab-canvas-title">
                        <h2 id="canvas-title"><?php _e('New Dashboard', 'db-app-builder'); ?></h2>
                        <span class="dab-canvas-status" id="canvas-status"><?php _e('Draft', 'db-app-builder'); ?></span>
                    </div>

                    <div class="dab-canvas-controls">
                        <button class="button" id="grid-toggle">
                            <span class="dashicons dashicons-grid-view"></span>
                            <?php _e('Toggle Grid', 'db-app-builder'); ?>
                        </button>

                        <button class="button" id="fullscreen-toggle">
                            <span class="dashicons dashicons-fullscreen-alt"></span>
                            <?php _e('Fullscreen', 'db-app-builder'); ?>
                        </button>
                    </div>
                </div>

                <div class="dab-grid-container" id="dashboard-grid">
                    <div class="dab-grid-background"></div>
                    <div class="dab-drop-zone">
                        <div class="dab-drop-message">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <p><?php _e('Drag widgets here to start building your dashboard', 'db-app-builder'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php elseif ($action === 'view'): ?>
        <div class="dab-dashboard-viewer" id="dashboard-viewer">
            <div class="dab-viewer-header">
                <div class="dab-viewer-title">
                    <h2 id="viewer-title"><?php _e('Dashboard', 'db-app-builder'); ?></h2>
                    <span class="dab-last-updated" id="last-updated"></span>
                </div>

                <div class="dab-viewer-controls">
                    <button class="button" id="refresh-data">
                        <span class="dashicons dashicons-update"></span>
                        <?php _e('Refresh', 'db-app-builder'); ?>
                    </button>

                    <button class="button" id="share-dashboard">
                        <span class="dashicons dashicons-share"></span>
                        <?php _e('Share', 'db-app-builder'); ?>
                    </button>

                    <button class="button" id="export-data">
                        <span class="dashicons dashicons-download"></span>
                        <?php _e('Export', 'db-app-builder'); ?>
                    </button>

                    <button class="button" id="fullscreen-view">
                        <span class="dashicons dashicons-fullscreen-alt"></span>
                        <?php _e('Fullscreen', 'db-app-builder'); ?>
                    </button>
                </div>
            </div>

            <div class="dab-dashboard-content" id="dashboard-content">
                <!-- Dashboard widgets will be rendered here -->
            </div>
        </div>

    <?php endif; ?>
</div>

<style>
.dab-analytics-dashboard-page {
    background: #f1f1f1;
    margin: 0 -20px;
    padding: 20px;
}

.dab-phase-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 10px;
}

.dab-dashboard-overview {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.dab-overview-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.dab-card-icon {
    font-size: 2.5em;
    margin-right: 20px;
    opacity: 0.8;
}

.dab-card-number {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.dab-card-label {
    font-size: 0.9em;
    opacity: 0.9;
}

.dab-dashboards-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dab-toolbar-filters {
    display: flex;
    gap: 10px;
    align-items: center;
}

.dab-toolbar-actions {
    display: flex;
    gap: 10px;
}

.dab-dashboards-grid {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 400px;
}

.dab-loading {
    text-align: center;
    padding: 50px;
}

.dab-dashboard-builder {
    display: flex;
    height: calc(100vh - 120px);
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.dab-builder-sidebar {
    width: 300px;
    background: #f8f9fa;
    border-right: 1px solid #e1e1e1;
    overflow-y: auto;
}

.dab-sidebar-section {
    padding: 20px;
    border-bottom: 1px solid #e1e1e1;
}

.dab-sidebar-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1em;
}

.dab-form-group {
    margin-bottom: 15px;
}

.dab-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.dab-input, .dab-textarea, .dab-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.dab-widget-library {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.dab-widget-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.3s ease;
    text-align: center;
}

.dab-widget-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.dab-widget-item .dashicons {
    font-size: 24px;
    margin-bottom: 8px;
    color: #667eea;
}

.dab-widget-item span:last-child {
    font-size: 12px;
    color: #666;
}

.dab-action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dab-dashboard-canvas {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.dab-canvas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e1e1;
    background: #f8f9fa;
}

.dab-canvas-title h2 {
    margin: 0;
    color: #333;
}

.dab-canvas-status {
    background: #ffc107;
    color: #333;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 10px;
}

.dab-canvas-controls {
    display: flex;
    gap: 10px;
}

.dab-grid-container {
    flex: 1;
    position: relative;
    overflow: auto;
    background: #fafafa;
}

.dab-grid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
}

.dab-drop-zone {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dab-drop-message {
    text-align: center;
    color: #999;
}

.dab-drop-message .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
}

.dab-dashboard-viewer {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.dab-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e1e1;
    background: #f8f9fa;
}

.dab-viewer-title h2 {
    margin: 0;
    color: #333;
}

.dab-last-updated {
    font-size: 12px;
    color: #666;
    margin-left: 10px;
}

.dab-viewer-controls {
    display: flex;
    gap: 10px;
}

.dab-dashboard-content {
    padding: 20px;
    min-height: 500px;
}

/* Drag and Drop Enhancements */
.dab-widget-item.ui-draggable-dragging {
    opacity: 0.8;
    transform: scale(1.05);
    z-index: 1000;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.dab-grid-container.ui-droppable-hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.dab-grid-container.ui-droppable-hover .dab-drop-zone {
    background-color: rgba(102, 126, 234, 0.1);
    border: 2px dashed #667eea;
    border-radius: 8px;
}

/* Widget Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dab-widget {
    transition: all 0.3s ease;
}

.dab-widget:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dab-widget.ui-draggable-dragging {
    opacity: 0.8;
    z-index: 1000;
    transform: scale(1.02);
}

.dab-widget.ui-resizable-resizing {
    opacity: 0.9;
}

/* Widget Actions */
.dab-widget-actions button:hover {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

/* Drop Zone Improvements */
.dab-drop-zone {
    transition: all 0.3s ease;
}

.dab-drop-message {
    transition: all 0.3s ease;
}

.dab-grid-container.ui-droppable-hover .dab-drop-message {
    color: #667eea;
    transform: scale(1.05);
}

/* Widget Library Improvements */
.dab-widget-item:active {
    transform: scale(0.98);
}

.dab-widget-item.ui-draggable-helper {
    background: white;
    border: 2px solid #667eea;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    transform: scale(1.1);
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize the analytics dashboard
    DAB_AnalyticsDashboard.init();
});
</script>
