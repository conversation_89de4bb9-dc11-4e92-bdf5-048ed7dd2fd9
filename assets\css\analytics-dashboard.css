/**
 * Analytics Dashboard CSS
 * Phase 3: Data Intelligence & Analytics
 */

/* Basic Reset and Layout */
.dab-analytics-dashboard-page {
    background: #f1f1f1;
    margin: 0 -20px;
    padding: 20px;
}

/* Widget Drag and Drop Enhancements */
.dab-widget-item {
    cursor: grab;
    transition: all 0.3s ease;
    user-select: none;
}

.dab-widget-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dab-widget-item:active {
    cursor: grabbing;
}

.dab-widget-item.ui-draggable-dragging {
    opacity: 0.8;
    transform: scale(1.05);
    z-index: 1000;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.dab-widget-item.ui-draggable-helper {
    background: white;
    border: 2px solid #667eea;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
    transform: scale(1.1);
}

/* Grid Container Enhancements */
.dab-grid-container {
    position: relative;
    min-height: 400px;
    background: #fafafa;
    border-radius: 8px;
    overflow: auto;
}

.dab-grid-container.ui-droppable-hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.dab-grid-container.ui-droppable-hover .dab-drop-zone {
    background-color: rgba(102, 126, 234, 0.1);
    border: 2px dashed #667eea;
    border-radius: 8px;
}

/* Drop Zone Styling */
.dab-drop-zone {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.dab-drop-message {
    text-align: center;
    color: #999;
    transition: all 0.3s ease;
}

.dab-grid-container.ui-droppable-hover .dab-drop-message {
    color: #667eea;
    transform: scale(1.05);
}

.dab-drop-message .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
}

/* Widget Styling */
.dab-widget {
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: absolute;
}

.dab-widget:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dab-widget.ui-draggable-dragging {
    opacity: 0.8;
    z-index: 1000;
    transform: scale(1.02);
}

.dab-widget.ui-resizable-resizing {
    opacity: 0.9;
}

.dab-widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 6px 6px 0 0;
    cursor: move;
}

.dab-widget-title {
    margin: 0;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
}

.dab-widget-title .dashicons {
    margin-right: 5px;
}

.dab-widget-actions {
    display: flex;
    gap: 5px;
}

.dab-widget-actions button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.dab-widget-actions button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dab-widget-actions .dashicons {
    font-size: 16px;
    color: #666;
}

.dab-widget-actions .dab-widget-remove .dashicons {
    color: #d63638;
}

.dab-widget-content {
    padding: 15px;
    height: calc(100% - 50px);
    overflow: auto;
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dab-widget-loading .dashicons-update {
    animation: spin 1s linear infinite;
}

/* Grid Background */
.dab-grid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
    pointer-events: none;
}

/* Resizable Handle Styling */
.dab-widget .ui-resizable-handle {
    background: #667eea;
    border: 1px solid #5a6fd8;
}

.dab-widget .ui-resizable-se {
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px;
    border-radius: 0 0 6px 0;
}

/* Widget Content Types */
.dab-metric-widget {
    text-align: center;
}

.dab-metric-value {
    font-size: 2em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.dab-metric-label {
    color: #666;
    font-size: 0.9em;
}

.dab-metric-change {
    font-size: 0.8em;
    margin-top: 5px;
}

.dab-metric-change.positive {
    color: #28a745;
}

.dab-metric-change.negative {
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dab-dashboard-builder {
        flex-direction: column;
        height: auto;
    }

    .dab-builder-sidebar {
        width: 100%;
        order: 2;
    }

    .dab-dashboard-canvas {
        order: 1;
        min-height: 400px;
    }

    .dab-widget-library {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .dab-widget-library {
        grid-template-columns: repeat(2, 1fr);
    }

    .dab-widget-item {
        padding: 10px 5px;
    }

    .dab-widget-item .dashicons {
        font-size: 20px;
    }

    .dab-widget-item span:last-child {
        font-size: 11px;
    }
}

/* Accessibility Improvements */
.dab-widget-item:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.dab-widget-actions button:focus {
    outline: 2px solid #667eea;
    outline-offset: 1px;
}

/* Button States */
.button.active {
    background: #667eea !important;
    color: white !important;
    border-color: #5a6fd8 !important;
}

.button.active:hover {
    background: #5a6fd8 !important;
}

/* Canvas Status Styling */
.dab-canvas-status.saved {
    background: #28a745;
    color: white;
}

.dab-canvas-status.draft {
    background: #ffc107;
    color: #333;
}

/* Modal Enhancements */
.dab-edit-widget-modal,
.dab-preview-modal {
    backdrop-filter: blur(2px);
}

.dab-modal-content {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.dab-modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
}

/* Form Enhancements */
.dab-input:focus,
.dab-textarea:focus,
.dab-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    outline: none;
}

/* Success/Error Messages */
.dab-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10001;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    animation: notificationSlideIn 0.3s ease-out;
}

.dab-notification-success {
    background: #28a745;
}

.dab-notification-error {
    background: #dc3545;
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading States */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Print Styles */
@media print {
    .dab-builder-sidebar,
    .dab-canvas-header,
    .dab-widget-actions {
        display: none !important;
    }

    .dab-dashboard-canvas {
        width: 100% !important;
    }

    .dab-widget {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}
