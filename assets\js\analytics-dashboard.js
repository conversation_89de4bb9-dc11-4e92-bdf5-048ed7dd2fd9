/**
 * Analytics Dashboard JavaScript
 * Phase 3: Data Intelligence & Analytics
 */

(function($) {
    'use strict';

    // Main Analytics Dashboard Object
    window.DAB_AnalyticsDashboard = {

        // Configuration
        config: {
            ajaxUrl: ajaxurl,
            nonce: dab_admin_vars.nonce,
            refreshInterval: 30000, // 30 seconds
            autoRefresh: true
        },

        // State management
        state: {
            currentDashboard: null,
            widgets: {},
            refreshTimer: null,
            isFullscreen: false
        },

        // Initialize the dashboard
        init: function() {
            this.bindEvents();
            this.loadDashboards();
            this.initializeComponents();

            // Start auto-refresh if enabled
            if (this.config.autoRefresh) {
                this.startAutoRefresh();
            }
        },

        // Bind event handlers
        bindEvents: function() {
            var self = this;

            // Dashboard list events
            $(document).on('click', '.dab-dashboard-card', function() {
                var dashboardId = $(this).data('dashboard-id');
                self.viewDashboard(dashboardId);
            });

            // Dashboard builder events
            $(document).on('click', '#save-dashboard', function() {
                self.saveDashboard();
            });

            $(document).on('click', '#preview-dashboard', function() {
                self.previewDashboard();
            });

            $(document).on('click', '#export-dashboard', function() {
                self.exportDashboard();
            });

            // Widget events
            $(document).on('click', '.dab-widget-item', function() {
                self.addWidget($(this).data('widget-type'));
            });

            $(document).on('click', '.dab-widget-remove', function() {
                var widgetId = $(this).closest('.dab-widget').data('widget-id');
                self.removeWidget(widgetId);
            });

            $(document).on('click', '.dab-widget-edit', function() {
                var widgetId = $(this).closest('.dab-widget').data('widget-id');
                self.editWidget(widgetId);
            });

            // Refresh events
            $(document).on('click', '#refresh-dashboards', function() {
                self.loadDashboards();
            });

            $(document).on('click', '#refresh-data', function() {
                self.refreshDashboardData();
            });

            // Fullscreen events
            $(document).on('click', '#fullscreen-toggle, #fullscreen-view', function() {
                self.toggleFullscreen();
            });

            // Grid events
            $(document).on('click', '#grid-toggle', function() {
                self.toggleGrid();
            });

            // Share events
            $(document).on('click', '#share-dashboard', function() {
                self.shareDashboard();
            });

            // Filter events
            $(document).on('change', '#dashboard-filter, #search-dashboards', function() {
                self.filterDashboards();
            });
        },

        // Initialize components
        initializeComponents: function() {
            // Initialize drag and drop for widgets
            this.initializeDragDrop();

            // Initialize grid system
            this.initializeGrid();

            // Initialize real-time updates
            this.initializeRealTime();
        },

        // Load dashboards list
        loadDashboards: function() {
            var self = this;

            $('#dashboards-grid').html('<div class="dab-loading"><div class="spinner is-active"></div><p>Loading dashboards...</p></div>');

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_dashboards_list',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.renderDashboardsList(response.data);
                        self.updateOverviewStats(response.data.stats);
                    } else {
                        self.showError('Failed to load dashboards: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Network error while loading dashboards');
                }
            });
        },

        // Render dashboards list
        renderDashboardsList: function(data) {
            var html = '';

            if (data.dashboards && data.dashboards.length > 0) {
                data.dashboards.forEach(function(dashboard) {
                    html += '<div class="dab-dashboard-card" data-dashboard-id="' + dashboard.id + '">';
                    html += '<div class="dab-card-header">';
                    html += '<h3>' + dashboard.name + '</h3>';
                    html += '<div class="dab-card-status ' + (dashboard.is_public ? 'public' : 'private') + '">';
                    html += dashboard.is_public ? 'Public' : 'Private';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="dab-card-content">';
                    html += '<p>' + (dashboard.description || 'No description') + '</p>';
                    html += '<div class="dab-card-meta">';
                    html += '<span><i class="dashicons dashicons-chart-line"></i> ' + dashboard.widget_count + ' widgets</span>';
                    html += '<span><i class="dashicons dashicons-visibility"></i> ' + dashboard.access_count + ' views</span>';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="dab-card-actions">';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.viewDashboard(' + dashboard.id + ')">View</button>';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.editDashboard(' + dashboard.id + ')">Edit</button>';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.duplicateDashboard(' + dashboard.id + ')">Duplicate</button>';
                    html += '</div>';
                    html += '</div>';
                });
            } else {
                html = '<div class="dab-empty-state">';
                html += '<div class="dab-empty-icon"><span class="dashicons dashicons-dashboard"></span></div>';
                html += '<h3>No dashboards found</h3>';
                html += '<p>Create your first analytics dashboard to get started.</p>';
                html += '<a href="' + window.location.href + '&action=create" class="button button-primary">Create Dashboard</a>';
                html += '</div>';
            }

            $('#dashboards-grid').html(html);
        },

        // Update overview statistics
        updateOverviewStats: function(stats) {
            $('#total-dashboards').text(stats.total_dashboards || 0);
            $('#active-widgets').text(stats.active_widgets || 0);
            $('#total-views').text(stats.total_views || 0);
            $('#realtime-updates').text(stats.realtime_updates || 0);
        },

        // View dashboard
        viewDashboard: function(dashboardId) {
            window.location.href = window.location.href.split('&')[0] + '&action=view&dashboard_id=' + dashboardId;
        },

        // Edit dashboard
        editDashboard: function(dashboardId) {
            window.location.href = window.location.href.split('&')[0] + '&action=edit&dashboard_id=' + dashboardId;
        },

        // Duplicate dashboard
        duplicateDashboard: function(dashboardId) {
            var self = this;

            if (!confirm('Are you sure you want to duplicate this dashboard?')) {
                return;
            }

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_duplicate_dashboard',
                    dashboard_id: dashboardId,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccess('Dashboard duplicated successfully');
                        self.loadDashboards();
                    } else {
                        self.showError('Failed to duplicate dashboard: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Network error while duplicating dashboard');
                }
            });
        },

        // Save dashboard
        saveDashboard: function() {
            var self = this;
            var dashboardData = this.collectDashboardData();

            if (!this.validateDashboardData(dashboardData)) {
                return;
            }

            // Show loading state
            var $saveBtn = $('#save-dashboard');
            var originalText = $saveBtn.html();
            $saveBtn.html('<span class="dashicons dashicons-update" style="animation: spin 1s linear infinite;"></span> Saving...').prop('disabled', true);

            // For now, simulate saving since we don't have the backend endpoint
            setTimeout(function() {
                try {
                    // Validate dashboard data
                    if (!dashboardData.name || dashboardData.name.trim() === '') {
                        throw new Error('Dashboard name is required');
                    }

                    // Simulate successful save
                    self.showSuccess('Dashboard saved successfully');
                    self.state.currentDashboard = 'dashboard_' + Date.now();
                    $('#canvas-status').text('Saved').removeClass('draft').addClass('saved');

                    // Store dashboard data in localStorage for demo purposes
                    var savedDashboards = JSON.parse(localStorage.getItem('dab_saved_dashboards') || '[]');
                    var dashboardToSave = {
                        id: self.state.currentDashboard,
                        name: dashboardData.name,
                        description: dashboardData.description,
                        widgets: self.state.widgets,
                        settings: dashboardData,
                        saved_at: new Date().toISOString()
                    };

                    // Remove existing dashboard with same name
                    savedDashboards = savedDashboards.filter(function(d) { return d.name !== dashboardData.name; });
                    savedDashboards.push(dashboardToSave);
                    localStorage.setItem('dab_saved_dashboards', JSON.stringify(savedDashboards));

                } catch (error) {
                    self.showError('Failed to save dashboard: ' + error.message);
                } finally {
                    // Always restore button state
                    $saveBtn.html(originalText).prop('disabled', false);
                }

                // In a real implementation, you would make an AJAX call like this:
                /*
                $.ajax({
                    url: self.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'dab_save_analytics_dashboard',
                        dashboard_data: JSON.stringify(dashboardData),
                        nonce: self.config.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            self.showSuccess('Dashboard saved successfully');
                            self.state.currentDashboard = response.data.dashboard_id;
                            $('#canvas-status').text('Saved').removeClass('draft').addClass('saved');
                        } else {
                            self.showError('Failed to save dashboard: ' + response.data);
                        }
                    },
                    error: function() {
                        self.showError('Network error while saving dashboard');
                    },
                    complete: function() {
                        $saveBtn.html(originalText).prop('disabled', false);
                    }
                });
                */
            }, 1500);
        },

        // Collect dashboard data from form
        collectDashboardData: function() {
            return {
                name: $('#dashboard-name').val(),
                description: $('#dashboard-description').val(),
                refresh_interval: $('#refresh-interval').val(),
                auto_refresh: $('#auto-refresh').is(':checked') ? 1 : 0,
                is_public: $('#is-public').is(':checked') ? 1 : 0,
                layout_config: this.getLayoutConfig(),
                theme_config: this.getThemeConfig(),
                widgets: this.getWidgetsConfig()
            };
        },

        // Validate dashboard data
        validateDashboardData: function(data) {
            if (!data.name || data.name.trim() === '') {
                this.showError('Dashboard name is required');
                $('#dashboard-name').focus();
                return false;
            }

            return true;
        },

        // Get layout configuration
        getLayoutConfig: function() {
            // Collect layout configuration from the grid
            var layout = [];
            $('.dab-widget').each(function() {
                var $widget = $(this);
                layout.push({
                    widget_id: $widget.data('widget-id'),
                    x: parseInt($widget.css('left')) || 0,
                    y: parseInt($widget.css('top')) || 0,
                    width: parseInt($widget.css('width')) || 200,
                    height: parseInt($widget.css('height')) || 150
                });
            });
            return layout;
        },

        // Get theme configuration
        getThemeConfig: function() {
            return {
                background_color: '#ffffff',
                text_color: '#333333',
                accent_color: '#667eea',
                grid_size: 20
            };
        },

        // Get widgets configuration
        getWidgetsConfig: function() {
            var widgets = [];
            $('.dab-widget').each(function() {
                var $widget = $(this);
                var widgetId = $widget.data('widget-id');
                if (this.state.widgets[widgetId]) {
                    widgets.push(this.state.widgets[widgetId]);
                }
            }.bind(this));
            return widgets;
        },

        // Initialize drag and drop
        initializeDragDrop: function() {
            var self = this;

            // Make widgets draggable from library
            $('.dab-widget-item').draggable({
                helper: 'clone',
                revert: 'invalid',
                zIndex: 1000,
                cursor: 'move',
                opacity: 0.8,
                start: function(event, ui) {
                    $(this).addClass('ui-draggable-dragging');
                    $('#dashboard-grid').addClass('ui-droppable-hover');
                },
                stop: function(event, ui) {
                    $(this).removeClass('ui-draggable-dragging');
                    $('#dashboard-grid').removeClass('ui-droppable-hover');
                }
            });

            // Make dashboard grid droppable
            $('#dashboard-grid').droppable({
                accept: '.dab-widget-item',
                tolerance: 'pointer',
                hoverClass: 'ui-droppable-hover',
                drop: function(event, ui) {
                    var widgetType = ui.draggable.data('widget-type');
                    var position = {
                        left: event.pageX - $(this).offset().left,
                        top: event.pageY - $(this).offset().top
                    };
                    self.addWidget(widgetType, position);
                    $(this).removeClass('ui-droppable-hover');
                }
            });

            // Make existing widgets draggable
            $(document).on('mousedown', '.dab-widget', function() {
                var $widget = $(this);
                if (!$widget.hasClass('ui-draggable')) {
                    $widget.draggable({
                        handle: '.dab-widget-header',
                        containment: '#dashboard-grid',
                        grid: [20, 20],
                        cursor: 'move',
                        opacity: 0.8,
                        start: function() {
                            $(this).addClass('ui-draggable-dragging');
                        },
                        stop: function() {
                            $(this).removeClass('ui-draggable-dragging');
                            self.updateWidgetPosition($(this));
                        }
                    });
                }
            });
        },

        // Initialize grid system
        initializeGrid: function() {
            this.updateGridBackground();
        },

        // Update grid background
        updateGridBackground: function() {
            var gridSize = 20;
            var $grid = $('.dab-grid-background');
            $grid.css({
                'background-size': gridSize + 'px ' + gridSize + 'px'
            });
        },

        // Toggle grid visibility
        toggleGrid: function() {
            var $gridBackground = $('.dab-grid-background');
            var $toggleBtn = $('#grid-toggle');

            if ($gridBackground.is(':visible')) {
                // Hide grid
                $gridBackground.fadeOut(200);
                $toggleBtn.removeClass('active').find('.dashicons').removeClass('dashicons-grid-view').addClass('dashicons-visibility');
                $toggleBtn.attr('title', 'Show Grid');
                this.showSuccess('Grid hidden');
            } else {
                // Show grid
                $gridBackground.fadeIn(200);
                $toggleBtn.addClass('active').find('.dashicons').removeClass('dashicons-visibility').addClass('dashicons-grid-view');
                $toggleBtn.attr('title', 'Hide Grid');
                this.showSuccess('Grid visible');
            }
        },

        // Initialize real-time updates
        initializeRealTime: function() {
            // Set up WebSocket or polling for real-time updates
            this.setupRealTimePolling();
        },

        // Set up real-time polling
        setupRealTimePolling: function() {
            var self = this;

            if (this.state.refreshTimer) {
                clearInterval(this.state.refreshTimer);
            }

            this.state.refreshTimer = setInterval(function() {
                if (self.config.autoRefresh && self.state.currentDashboard) {
                    self.refreshDashboardData();
                }
            }, this.config.refreshInterval);
        },

        // Start auto-refresh
        startAutoRefresh: function() {
            this.config.autoRefresh = true;
            this.setupRealTimePolling();
        },

        // Stop auto-refresh
        stopAutoRefresh: function() {
            this.config.autoRefresh = false;
            if (this.state.refreshTimer) {
                clearInterval(this.state.refreshTimer);
                this.state.refreshTimer = null;
            }
        },

        // Refresh dashboard data
        refreshDashboardData: function() {
            var self = this;

            if (!this.state.currentDashboard) {
                return;
            }

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_realtime_data',
                    dashboard_id: this.state.currentDashboard,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.updateWidgetsData(response.data);
                        self.updateLastRefreshTime();
                    }
                },
                error: function() {
                    console.warn('Failed to refresh dashboard data');
                }
            });
        },

        // Update widgets with new data
        updateWidgetsData: function(data) {
            for (var widgetId in data) {
                if (data.hasOwnProperty(widgetId)) {
                    this.updateWidgetData(widgetId, data[widgetId]);
                }
            }
        },

        // Update individual widget data
        updateWidgetData: function(widgetId, data) {
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');
            if ($widget.length > 0) {
                // Update widget content based on type
                var widgetType = $widget.data('widget-type');
                this.renderWidgetData($widget, widgetType, data);
            }
        },

        // Render widget data based on type
        renderWidgetData: function($widget, type, data) {
            var $content = $widget.find('.dab-widget-content');

            switch (type) {
                case 'metric':
                    this.renderMetricWidget($content, data);
                    break;
                case 'chart':
                    this.renderChartWidget($content, data);
                    break;
                case 'table':
                    this.renderTableWidget($content, data);
                    break;
                case 'gauge':
                    this.renderGaugeWidget($content, data);
                    break;
                default:
                    $content.html('<p>Unknown widget type: ' + type + '</p>');
            }
        },

        // Render metric widget
        renderMetricWidget: function($content, data) {
            var html = '<div class="dab-metric-widget">';
            html += '<div class="dab-metric-value">' + (data.value || 0) + '</div>';
            html += '<div class="dab-metric-label">' + (data.label || 'Metric') + '</div>';
            if (data.change) {
                var changeClass = data.change > 0 ? 'positive' : 'negative';
                html += '<div class="dab-metric-change ' + changeClass + '">' + data.change + '%</div>';
            }
            html += '</div>';
            $content.html(html);
        },

        // Render chart widget
        renderChartWidget: function($content, data) {
            var html = '<div class="dab-chart-widget">';
            html += '<div class="dab-chart-placeholder">Chart will be rendered here</div>';
            html += '</div>';
            $content.html(html);
        },

        // Render table widget
        renderTableWidget: function($content, data) {
            var html = '<div class="dab-table-widget">';
            html += '<div class="dab-table-placeholder">Table will be rendered here</div>';
            html += '</div>';
            $content.html(html);
        },

        // Render gauge widget
        renderGaugeWidget: function($content, data) {
            var html = '<div class="dab-gauge-widget">';
            html += '<div class="dab-gauge-placeholder">Gauge will be rendered here</div>';
            html += '</div>';
            $content.html(html);
        },

        // Add widget to dashboard
        addWidget: function(widgetType, position) {
            var self = this;

            // Generate unique widget ID
            var widgetId = 'widget_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // Calculate position relative to grid
            var gridOffset = $('#dashboard-grid').offset();
            var relativeX = (position ? position.left : 100) - (gridOffset ? gridOffset.left : 0);
            var relativeY = (position ? position.top : 100) - (gridOffset ? gridOffset.top : 0);

            // Snap to grid
            var gridSize = 20;
            relativeX = Math.round(relativeX / gridSize) * gridSize;
            relativeY = Math.round(relativeY / gridSize) * gridSize;

            // Create widget HTML
            var widgetHtml = this.createWidgetHTML(widgetId, widgetType, relativeX, relativeY);

            // Add widget to grid
            $('#dashboard-grid').append(widgetHtml);

            // Hide drop zone message if this is the first widget
            $('.dab-drop-zone').hide();

            // Store widget in state
            this.state.widgets[widgetId] = {
                id: widgetId,
                type: widgetType,
                x: relativeX,
                y: relativeY,
                width: 200,
                height: 150,
                title: this.getWidgetTitle(widgetType),
                dataSource: '', // No data source selected by default
                refreshInterval: 0 // Manual refresh by default
            };

            // Initialize widget functionality
            this.initializeWidget(widgetId);

            // Mark dashboard as modified
            $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');

            console.log('Widget added:', widgetId, widgetType);
        },

        // Create widget HTML
        createWidgetHTML: function(widgetId, widgetType, x, y) {
            var title = this.getWidgetTitle(widgetType);
            var iconClass = this.getWidgetIcon(widgetType);

            var html = '<div class="dab-widget" data-widget-id="' + widgetId + '" data-widget-type="' + widgetType + '" ';
            html += 'style="position: absolute; left: ' + x + 'px; top: ' + y + 'px; width: 200px; height: 150px; ';
            html += 'background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';

            // Widget header
            html += '<div class="dab-widget-header" style="display: flex; justify-content: space-between; align-items: center; ';
            html += 'padding: 10px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 6px 6px 0 0; cursor: move;">';
            html += '<h3 class="dab-widget-title" style="margin: 0; font-size: 14px; color: #333;">';
            html += '<span class="dashicons ' + iconClass + '" style="margin-right: 5px;"></span>' + title;
            html += '</h3>';
            html += '<div class="dab-widget-actions">';
            html += '<button type="button" class="dab-widget-edit" title="Edit Widget" style="background: none; border: none; cursor: pointer; padding: 2px;">';
            html += '<span class="dashicons dashicons-edit" style="font-size: 16px; color: #666;"></span>';
            html += '</button>';
            html += '<button type="button" class="dab-widget-remove" title="Remove Widget" style="background: none; border: none; cursor: pointer; padding: 2px; margin-left: 5px;">';
            html += '<span class="dashicons dashicons-no-alt" style="font-size: 16px; color: #d63638;"></span>';
            html += '</button>';
            html += '</div>';
            html += '</div>';

            // Widget content
            html += '<div class="dab-widget-content" style="padding: 15px; height: calc(100% - 50px); overflow: auto;">';
            html += '<div class="dab-widget-loading" style="text-align: center; color: #666;">';
            html += '<span class="dashicons dashicons-update" style="animation: spin 1s linear infinite;"></span>';
            html += '<p style="margin: 10px 0 0 0;">Loading ' + title.toLowerCase() + '...</p>';
            html += '</div>';
            html += '</div>';

            html += '</div>';

            return html;
        },

        // Get widget title by type
        getWidgetTitle: function(type) {
            var titles = {
                'metric': 'Metric Card',
                'chart': 'Chart Widget',
                'table': 'Data Table',
                'gauge': 'Gauge Chart',
                'map': 'Map Widget',
                'text': 'Text Widget'
            };
            return titles[type] || 'Unknown Widget';
        },

        // Get widget icon by type
        getWidgetIcon: function(type) {
            var icons = {
                'metric': 'dashicons-chart-area',
                'chart': 'dashicons-chart-bar',
                'table': 'dashicons-list-view',
                'gauge': 'dashicons-performance',
                'map': 'dashicons-location',
                'text': 'dashicons-text'
            };
            return icons[type] || 'dashicons-admin-generic';
        },

        // Initialize widget functionality
        initializeWidget: function(widgetId) {
            var self = this;
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');

            // Make widget draggable
            $widget.draggable({
                handle: '.dab-widget-header',
                containment: '#dashboard-grid',
                grid: [20, 20],
                stop: function() {
                    self.updateWidgetPosition($widget);
                }
            });

            // Make widget resizable
            $widget.resizable({
                handles: 'se',
                minWidth: 150,
                minHeight: 100,
                grid: [20, 20],
                stop: function() {
                    self.updateWidgetSize($widget);
                }
            });

            // Load widget content
            setTimeout(function() {
                self.loadWidgetContent(widgetId);
            }, 500);
        },

        // Update widget position
        updateWidgetPosition: function($widget) {
            var widgetId = $widget.data('widget-id');
            var position = $widget.position();

            if (this.state.widgets[widgetId]) {
                this.state.widgets[widgetId].x = position.left;
                this.state.widgets[widgetId].y = position.top;

                // Mark dashboard as modified
                $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');
            }
        },

        // Update widget size
        updateWidgetSize: function($widget) {
            var widgetId = $widget.data('widget-id');
            var width = $widget.width();
            var height = $widget.height();

            if (this.state.widgets[widgetId]) {
                this.state.widgets[widgetId].width = width;
                this.state.widgets[widgetId].height = height;

                // Mark dashboard as modified
                $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');
            }
        },

        // Load widget content
        loadWidgetContent: function(widgetId) {
            var self = this;
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');
            var widgetType = $widget.data('widget-type');
            var $content = $widget.find('.dab-widget-content');
            var widget = this.state.widgets[widgetId];

            // Simulate loading delay
            setTimeout(function() {
                var hasDataSource = widget && widget.dataSource && widget.dataSource !== '';
                var dataSourceName = hasDataSource ? self.getDataSourceName(widget.dataSource) : 'No data source';

                if (!hasDataSource) {
                    // Show configuration needed message
                    $content.html('<div style="text-align: center; padding: 20px; color: #666;">' +
                        '<div style="margin-bottom: 10px;"><span class="dashicons dashicons-admin-settings" style="font-size: 24px;"></span></div>' +
                        '<p style="margin: 0 0 10px 0;">Configuration needed</p>' +
                        '<p style="margin: 0; font-size: 12px;">Click edit to configure data source</p>' +
                        '</div>');
                    return;
                }

                // Render widget based on type with data source info
                switch (widgetType) {
                    case 'metric':
                        $content.html('<div class="dab-metric-widget" style="text-align: center;">' +
                            '<div class="dab-metric-value" style="font-size: 2em; font-weight: bold; color: #667eea;">42</div>' +
                            '<div class="dab-metric-label" style="color: #666; margin-top: 5px;">Sample Metric</div>' +
                            '<div style="font-size: 11px; color: #999; margin-top: 5px;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'chart':
                        $content.html('<div class="dab-chart-widget" style="text-align: center; padding: 15px;">' +
                            '<div style="background: #f0f0f0; height: 60px; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666; margin-bottom: 8px;">' +
                            '<span class="dashicons dashicons-chart-bar" style="font-size: 20px; margin-right: 8px;"></span>Chart Preview' +
                            '</div>' +
                            '<div style="font-size: 11px; color: #999;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'table':
                        $content.html('<div class="dab-table-widget">' +
                            '<table style="width: 100%; border-collapse: collapse; font-size: 12px;">' +
                            '<thead><tr style="background: #f8f9fa;"><th style="padding: 6px; border: 1px solid #ddd;">Column 1</th><th style="padding: 6px; border: 1px solid #ddd;">Column 2</th></tr></thead>' +
                            '<tbody><tr><td style="padding: 6px; border: 1px solid #ddd;">Data 1</td><td style="padding: 6px; border: 1px solid #ddd;">Data 2</td></tr></tbody>' +
                            '</table>' +
                            '<div style="font-size: 11px; color: #999; margin-top: 5px; text-align: center;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'gauge':
                        $content.html('<div class="dab-gauge-widget" style="text-align: center; padding: 15px;">' +
                            '<div style="width: 60px; height: 60px; border: 6px solid #667eea; border-radius: 50%; margin: 0 auto 8px auto; display: flex; align-items: center; justify-content: center; color: #667eea; font-weight: bold; font-size: 14px;">75%</div>' +
                            '<div style="font-size: 11px; color: #999;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'map':
                        $content.html('<div class="dab-map-widget" style="text-align: center; padding: 15px;">' +
                            '<div style="background: #e8f4f8; height: 60px; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666; margin-bottom: 8px;">' +
                            '<span class="dashicons dashicons-location" style="font-size: 20px; margin-right: 8px;"></span>Map Preview' +
                            '</div>' +
                            '<div style="font-size: 11px; color: #999;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'text':
                        $content.html('<div class="dab-text-widget">' +
                            '<p style="margin: 0 0 8px 0; color: #333; font-size: 13px;">This is a sample text widget. You can add custom content here.</p>' +
                            '<div style="font-size: 11px; color: #999;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    default:
                        $content.html('<div style="text-align: center; color: #666; padding: 20px;">Unknown widget type: ' + widgetType + '</div>');
                }
            }, 300);
        },

        // Get data source name by ID
        getDataSourceName: function(dataSourceId) {
            var dataSources = {
                'users': 'WordPress Users',
                'posts': 'WordPress Posts',
                'comments': 'WordPress Comments',
                'orders': 'WooCommerce Orders',
                'products': 'WooCommerce Products',
                'custom_table_1': 'Custom Table 1',
                'custom_table_2': 'Custom Table 2'
            };
            return dataSources[dataSourceId] || dataSourceId;
        },

        // Remove widget
        removeWidget: function(widgetId) {
            if (confirm('Are you sure you want to remove this widget?')) {
                $('.dab-widget[data-widget-id="' + widgetId + '"]').remove();
                delete this.state.widgets[widgetId];

                // Show drop zone if no widgets left
                if (Object.keys(this.state.widgets).length === 0) {
                    $('.dab-drop-zone').show();
                }

                // Mark dashboard as modified
                $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');
            }
        },

        // Edit widget
        editWidget: function(widgetId) {
            var self = this;
            var widget = this.state.widgets[widgetId];

            if (!widget) {
                this.showError('Widget not found');
                return;
            }

            // Create edit modal
            this.showEditWidgetModal(widget);
        },

        // Show edit widget modal
        showEditWidgetModal: function(widget) {
            var self = this;

            // Remove existing modal if any
            $('.dab-edit-widget-modal').remove();

            // Get available data sources
            this.loadDataSources(function(dataSources) {
                var dataSourceOptions = '';
                if (dataSources && dataSources.length > 0) {
                    dataSources.forEach(function(source) {
                        var selected = widget.dataSource === source.id ? 'selected' : '';
                        dataSourceOptions += `<option value="${source.id}" ${selected}>${source.name}</option>`;
                    });
                } else {
                    dataSourceOptions = '<option value="">No data sources available</option>';
                }

                var modalHtml = `
                    <div class="dab-edit-widget-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div class="dab-modal-content" style="background: white; border-radius: 8px; padding: 20px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                            <div class="dab-modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                                <h3 style="margin: 0;">Edit ${widget.title}</h3>
                                <button type="button" class="dab-modal-close" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">&times;</button>
                            </div>
                            <div class="dab-modal-body">
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Widget Title:</label>
                                    <input type="text" id="edit-widget-title" value="${widget.title}" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Widget Type:</label>
                                    <select id="edit-widget-type" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="metric" ${widget.type === 'metric' ? 'selected' : ''}>Metric Card</option>
                                        <option value="chart" ${widget.type === 'chart' ? 'selected' : ''}>Chart Widget</option>
                                        <option value="table" ${widget.type === 'table' ? 'selected' : ''}>Data Table</option>
                                        <option value="gauge" ${widget.type === 'gauge' ? 'selected' : ''}>Gauge Chart</option>
                                        <option value="map" ${widget.type === 'map' ? 'selected' : ''}>Map Widget</option>
                                        <option value="text" ${widget.type === 'text' ? 'selected' : ''}>Text Widget</option>
                                    </select>
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Data Source:</label>
                                    <select id="edit-widget-datasource" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="">Select a data source...</option>
                                        ${dataSourceOptions}
                                    </select>
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Refresh Interval:</label>
                                    <select id="edit-widget-refresh" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="0" ${(widget.refreshInterval || 0) == 0 ? 'selected' : ''}>Manual only</option>
                                        <option value="30" ${widget.refreshInterval == 30 ? 'selected' : ''}>30 seconds</option>
                                        <option value="60" ${widget.refreshInterval == 60 ? 'selected' : ''}>1 minute</option>
                                        <option value="300" ${widget.refreshInterval == 300 ? 'selected' : ''}>5 minutes</option>
                                        <option value="900" ${widget.refreshInterval == 900 ? 'selected' : ''}>15 minutes</option>
                                    </select>
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Width (px):</label>
                                    <input type="number" id="edit-widget-width" value="${widget.width}" min="150" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Height (px):</label>
                                    <input type="number" id="edit-widget-height" value="${widget.height}" min="100" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                            </div>
                            <div class="dab-modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; padding-top: 15px; border-top: 1px solid #eee;">
                                <button type="button" class="button dab-modal-cancel">Cancel</button>
                                <button type="button" class="button button-primary dab-save-widget-changes" data-widget-id="${widget.id}">Save Changes</button>
                            </div>
                        </div>
                    </div>
                `;

                $('body').append(modalHtml);

                // Bind modal events
                $('.dab-modal-close, .dab-modal-cancel').on('click', function() {
                    $('.dab-edit-widget-modal').remove();
                });

                $('.dab-edit-widget-modal').on('click', function(e) {
                    if (e.target === this) {
                        $(this).remove();
                    }
                });

                $('.dab-save-widget-changes').on('click', function() {
                    self.saveWidgetChanges($(this).data('widget-id'));
                });
            });
        },

        // Load available data sources
        loadDataSources: function(callback) {
            var self = this;

            // For now, return mock data sources. In a real implementation, this would be an AJAX call
            var mockDataSources = [
                { id: 'users', name: 'WordPress Users' },
                { id: 'posts', name: 'WordPress Posts' },
                { id: 'comments', name: 'WordPress Comments' },
                { id: 'orders', name: 'WooCommerce Orders' },
                { id: 'products', name: 'WooCommerce Products' },
                { id: 'custom_table_1', name: 'Custom Table 1' },
                { id: 'custom_table_2', name: 'Custom Table 2' }
            ];

            // Simulate async loading
            setTimeout(function() {
                callback(mockDataSources);
            }, 100);

            // Real implementation would look like this:
            /*
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_data_sources',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        callback(response.data);
                    } else {
                        callback([]);
                    }
                },
                error: function() {
                    callback([]);
                }
            });
            */
        },

        // Save widget changes
        saveWidgetChanges: function(widgetId) {
            var widget = this.state.widgets[widgetId];
            if (!widget) return;

            var newTitle = $('#edit-widget-title').val();
            var newType = $('#edit-widget-type').val();
            var newDataSource = $('#edit-widget-datasource').val();
            var newRefreshInterval = parseInt($('#edit-widget-refresh').val());
            var newWidth = parseInt($('#edit-widget-width').val());
            var newHeight = parseInt($('#edit-widget-height').val());

            // Update widget state
            widget.title = newTitle;
            widget.type = newType;
            widget.dataSource = newDataSource;
            widget.refreshInterval = newRefreshInterval;
            widget.width = newWidth;
            widget.height = newHeight;

            // Update widget in DOM
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');
            $widget.attr('data-widget-type', newType);
            $widget.find('.dab-widget-title').html('<span class="dashicons ' + this.getWidgetIcon(newType) + '" style="margin-right: 5px;"></span>' + newTitle);
            $widget.css({
                width: newWidth + 'px',
                height: newHeight + 'px'
            });

            // Reload widget content
            this.loadWidgetContent(widgetId);

            // Close modal
            $('.dab-edit-widget-modal').remove();

            // Mark dashboard as modified
            $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');

            this.showSuccess('Widget updated successfully');
        },

        // Preview dashboard
        previewDashboard: function() {
            var self = this;

            // Create preview modal
            this.showPreviewModal();
        },

        // Show preview modal
        showPreviewModal: function() {
            var self = this;

            // Remove existing modal if any
            $('.dab-preview-modal').remove();

            // Get current dashboard content
            var dashboardContent = this.generatePreviewContent();

            var modalHtml = `
                <div class="dab-preview-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                    <div class="dab-modal-content" style="background: white; border-radius: 8px; padding: 0; max-width: 90%; width: 1200px; max-height: 90vh; overflow: hidden; display: flex; flex-direction: column;">
                        <div class="dab-modal-header" style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee;">
                            <h3 style="margin: 0;">Dashboard Preview</h3>
                            <button type="button" class="dab-modal-close" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">&times;</button>
                        </div>
                        <div class="dab-modal-body" style="flex: 1; overflow: auto; padding: 20px; background: #fafafa;">
                            ${dashboardContent}
                        </div>
                        <div class="dab-modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; padding: 20px; border-top: 1px solid #eee;">
                            <button type="button" class="button dab-modal-close">Close Preview</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);

            // Bind modal events
            $('.dab-modal-close').on('click', function() {
                $('.dab-preview-modal').remove();
            });

            $('.dab-preview-modal').on('click', function(e) {
                if (e.target === this) {
                    $(this).remove();
                }
            });
        },

        // Generate preview content
        generatePreviewContent: function() {
            var dashboardName = $('#dashboard-name').val() || 'Untitled Dashboard';
            var dashboardDescription = $('#dashboard-description').val() || 'No description provided';

            var html = `
                <div style="background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                    <h2 style="margin: 0 0 10px 0; color: #333;">${dashboardName}</h2>
                    <p style="margin: 0; color: #666;">${dashboardDescription}</p>
                </div>
                <div style="position: relative; background: #fafafa; border-radius: 8px; min-height: 400px; padding: 20px;">
            `;

            // Add widgets to preview
            if (Object.keys(this.state.widgets).length > 0) {
                for (var widgetId in this.state.widgets) {
                    var widget = this.state.widgets[widgetId];
                    html += `
                        <div style="position: absolute; left: ${widget.x}px; top: ${widget.y}px; width: ${widget.width}px; height: ${widget.height}px; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 6px 6px 0 0;">
                                <h4 style="margin: 0; font-size: 14px; color: #333;">
                                    <span class="dashicons ${this.getWidgetIcon(widget.type)}" style="margin-right: 5px;"></span>${widget.title}
                                </h4>
                            </div>
                            <div style="padding: 15px; height: calc(100% - 50px); overflow: auto;">
                                ${this.getPreviewWidgetContent(widget.type)}
                            </div>
                        </div>
                    `;
                }
            } else {
                html += `
                    <div style="display: flex; align-items: center; justify-content: center; height: 300px; color: #999; text-align: center;">
                        <div>
                            <span class="dashicons dashicons-plus-alt" style="font-size: 48px; margin-bottom: 10px;"></span>
                            <p>No widgets added to this dashboard yet.</p>
                        </div>
                    </div>
                `;
            }

            html += '</div>';
            return html;
        },

        // Get preview widget content
        getPreviewWidgetContent: function(type) {
            switch (type) {
                case 'metric':
                    return '<div style="text-align: center;"><div style="font-size: 2em; font-weight: bold; color: #667eea;">42</div><div style="color: #666; margin-top: 5px;">Sample Metric</div></div>';
                case 'chart':
                    return '<div style="text-align: center; padding: 20px;"><div style="background: #f0f0f0; height: 80px; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666;"><span class="dashicons dashicons-chart-bar" style="font-size: 24px; margin-right: 10px;"></span>Chart Preview</div></div>';
                case 'table':
                    return '<table style="width: 100%; border-collapse: collapse;"><thead><tr style="background: #f8f9fa;"><th style="padding: 8px; border: 1px solid #ddd;">Column 1</th><th style="padding: 8px; border: 1px solid #ddd;">Column 2</th></tr></thead><tbody><tr><td style="padding: 8px; border: 1px solid #ddd;">Data 1</td><td style="padding: 8px; border: 1px solid #ddd;">Data 2</td></tr></tbody></table>';
                case 'gauge':
                    return '<div style="text-align: center; padding: 20px;"><div style="width: 80px; height: 80px; border: 8px solid #667eea; border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: #667eea; font-weight: bold;">75%</div></div>';
                case 'map':
                    return '<div style="text-align: center; padding: 20px;"><div style="background: #e8f4f8; height: 80px; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666;"><span class="dashicons dashicons-location" style="font-size: 24px; margin-right: 10px;"></span>Map Preview</div></div>';
                case 'text':
                    return '<p style="margin: 0; color: #333;">This is a sample text widget. You can add custom content here.</p>';
                default:
                    return '<div style="text-align: center; color: #666; padding: 20px;">Unknown widget type: ' + type + '</div>';
            }
        },

        // Export dashboard
        exportDashboard: function() {
            var self = this;
            var dashboardData = this.collectDashboardData();

            if (!this.validateDashboardData(dashboardData)) {
                return;
            }

            // Create export data
            var exportData = {
                name: dashboardData.name,
                description: dashboardData.description,
                settings: {
                    refresh_interval: dashboardData.refresh_interval,
                    auto_refresh: dashboardData.auto_refresh,
                    is_public: dashboardData.is_public
                },
                widgets: this.state.widgets,
                layout: dashboardData.layout_config,
                theme: dashboardData.theme_config,
                exported_at: new Date().toISOString(),
                version: '1.0'
            };

            // Create and download file
            var dataStr = JSON.stringify(exportData, null, 2);
            var dataBlob = new Blob([dataStr], {type: 'application/json'});
            var url = URL.createObjectURL(dataBlob);

            var link = document.createElement('a');
            link.href = url;
            link.download = (dashboardData.name || 'dashboard') + '_export.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            this.showSuccess('Dashboard exported successfully');
        },

        // Share dashboard
        shareDashboard: function() {
            alert('Dashboard sharing functionality will be implemented in a future update.');
        },

        // Filter dashboards
        filterDashboards: function() {
            // Dashboard filtering functionality
            var filter = $('#dashboard-filter').val();
            var search = $('#search-dashboards').val().toLowerCase();

            $('.dab-dashboard-card').each(function() {
                var $card = $(this);
                var title = $card.find('h3').text().toLowerCase();
                var description = $card.find('p').text().toLowerCase();
                var isPublic = $card.find('.dab-card-status').hasClass('public');

                var matchesFilter = filter === 'all' ||
                    (filter === 'public' && isPublic) ||
                    (filter === 'private' && !isPublic);

                var matchesSearch = search === '' ||
                    title.includes(search) ||
                    description.includes(search);

                if (matchesFilter && matchesSearch) {
                    $card.show();
                } else {
                    $card.hide();
                }
            });
        },

        // Toggle fullscreen
        toggleFullscreen: function() {
            if (!this.state.isFullscreen) {
                // Enter fullscreen
                var elem = document.documentElement;
                if (elem.requestFullscreen) {
                    elem.requestFullscreen();
                } else if (elem.mozRequestFullScreen) {
                    elem.mozRequestFullScreen();
                } else if (elem.webkitRequestFullscreen) {
                    elem.webkitRequestFullscreen();
                } else if (elem.msRequestFullscreen) {
                    elem.msRequestFullscreen();
                }
                this.state.isFullscreen = true;
                $('#fullscreen-toggle, #fullscreen-view').find('.dashicons').removeClass('dashicons-fullscreen-alt').addClass('dashicons-fullscreen-exit-alt');
            } else {
                // Exit fullscreen
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                this.state.isFullscreen = false;
                $('#fullscreen-toggle, #fullscreen-view').find('.dashicons').removeClass('dashicons-fullscreen-exit-alt').addClass('dashicons-fullscreen-alt');
            }
        },

        // Update last refresh time
        updateLastRefreshTime: function() {
            var now = new Date();
            var timeString = now.toLocaleTimeString();
            $('#last-updated').text('Last updated: ' + timeString);
        },

        // Show success message
        showSuccess: function(message) {
            this.showNotification(message, 'success');
        },

        // Show error message
        showError: function(message) {
            this.showNotification(message, 'error');
        },

        // Show notification
        showNotification: function(message, type) {
            var $notification = $('<div class="dab-notification dab-notification-' + type + '">' + message + '</div>');
            $('body').append($notification);

            setTimeout(function() {
                $notification.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof dab_admin_vars !== 'undefined') {
            DAB_AnalyticsDashboard.init();
        }
    });

})(jQuery);
